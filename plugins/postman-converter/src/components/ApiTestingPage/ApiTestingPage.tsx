import React, { useState, useCallback } from 'react';
import {
  Grid,
  Typography,
  makeStyles,
  Box,
  Snackbar,
  Menu,
  MenuItem,
} from '@material-ui/core';
import {
  InfoCard,
} from '@backstage/core-components';
import { Alert } from '@material-ui/lab';


// Icons
import TestingIcon from '@material-ui/icons/BugReport';

// Types
import {
  ApiFolder,
} from '../../types';

// Utils
import { createNewRequest } from './utils/requestUtils';

// Hooks
import { useCollections } from './hooks/useCollections';
import { useRequest } from './hooks/useRequest';
import { useEnvironments } from './hooks/useEnvironments';

// Components
import { CollectionsSidebar } from './components/CollectionsSidebar/CollectionsSidebar';
import { RequestBuilder } from './components/RequestBuilder';
import { ResponseViewer } from './components/ResponseViewer';
import { EnvironmentManager } from './components/EnvironmentManager';
import { ResizablePanels } from './components/ResizablePanels';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { CreateFolderDialog } from './CreateFolderDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { RenameDialog } from './components/RenameDialog';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
  mainContent: {
    flex: 1,
    minHeight: 0, // Important for flex children to shrink
  },
  requestResponseContainer: {
    height: 'calc(100vh - 300px)', // Adjust based on header and other elements
    minHeight: '600px',
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();

  // Collections management
  const {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    unsavedCollections,
    isSaving,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
    handleSaveCollection,
  } = useCollections();

  // Handle save all collections
  const handleSaveAllCollections = useCallback(async () => {
    const savePromises = Array.from(unsavedCollections).map(collectionId =>
      handleSaveCollection(collectionId)
    );
    await Promise.all(savePromises);
  }, [unsavedCollections, handleSaveCollection]);

  // Request management
  const {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    isLoading,
    tabValue,
    responseTabValue,
    isGeneratingTests,
    isRunningTests,
    testError,
    isSavingPreRequestScript,
    preRequestScriptError,
    handleTabChange,
    handleResponseTabChange,
    handleMethodChange,
    handleUrlChange,
    handleSendRequest,
    handleGenerateTests,
    handleRunTests,
    handleSaveTests,
    handleSavePreRequestScript,
  } = useRequest(collections, setCollections);

  // Environment management
  const {
    environments,
    setEnvironments,
    currentEnvironment,
    setCurrentEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    handleImportEnvironment,
  } = useEnvironments();

  // Local state for dialogs and UI
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    itemId: string;
    itemType: 'collection' | 'folder' | 'request';
  } | null>(null);

  // Dialog states
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameDialogData, setRenameDialogData] = useState<{
    itemType: 'collection' | 'folder' | 'request';
    itemId: string;
    collectionId: string;
    currentName: string;
  } | null>(null);
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState<string>('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState<string>('');
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string>(environments[0]?.id || '');

  // Handle snackbar close
  const handleSnackbarClose = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // Handle environment change
  const handleEnvironmentChange = useCallback((event: React.ChangeEvent<{ value: unknown }>) => {
    setCurrentEnvironment(event.target.value as string);
  }, [setCurrentEnvironment]);

  // Handle context menu
  const handleContextMenu = useCallback((
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  }, []);

  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Handle context menu actions
  const handleAddFolderFromContext = useCallback(() => {
    if (contextMenu) {
      if (contextMenu.itemType === 'collection') {
        setSelectedCollectionForAction(contextMenu.itemId);
        setSelectedFolderForAction('');
      } else if (contextMenu.itemType === 'folder') {
        // Find the collection that contains this folder
        const collection = collections.find(c => {
          const findFolder = (folders: ApiFolder[]): boolean => {
            return folders.some(f =>
              f.id === contextMenu.itemId ||
              (f.folders.length > 0 && findFolder(f.folders))
            );
          };
          return findFolder(c.folders);
        });
        if (collection) {
          setSelectedCollectionForAction(collection.id);
          setSelectedFolderForAction(contextMenu.itemId);
        }
      }
      setCreateFolderDialogOpen(true);
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose]);

  const handleAddRequestFromContext = useCallback(() => {
    if (contextMenu) {
      if (contextMenu.itemType === 'collection') {
        setSelectedCollectionForAction(contextMenu.itemId);
        setSelectedFolderForAction('');
      } else if (contextMenu.itemType === 'folder') {
        // Find the collection that contains this folder
        const collection = collections.find(c => {
          const findFolder = (folders: ApiFolder[]): boolean => {
            return folders.some(f =>
              f.id === contextMenu.itemId ||
              (f.folders.length > 0 && findFolder(f.folders))
            );
          };
          return findFolder(c.folders);
        });
        if (collection) {
          setSelectedCollectionForAction(collection.id);
          setSelectedFolderForAction(contextMenu.itemId);
        }
      }
      setCreateRequestDialogOpen(true);
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose]);

  const handleRenameFromContext = useCallback(() => {
    if (contextMenu) {
      let currentName = '';
      let collectionId = '';

      if (contextMenu.itemType === 'collection') {
        const collection = collections.find(c => c.id === contextMenu.itemId);
        if (collection) {
          currentName = collection.name;
          collectionId = collection.id;
        }
      } else if (contextMenu.itemType === 'folder') {
        // Find the collection and folder
        for (const collection of collections) {
          const findFolder = (folders: ApiFolder[]): ApiFolder | null => {
            for (const folder of folders) {
              if (folder.id === contextMenu.itemId) {
                return folder;
              }
              if (folder.folders.length > 0) {
                const found = findFolder(folder.folders);
                if (found) return found;
              }
            }
            return null;
          };
          const folder = findFolder(collection.folders);
          if (folder) {
            currentName = folder.name;
            collectionId = collection.id;
            break;
          }
        }
      } else if (contextMenu.itemType === 'request') {
        // Find the collection and request
        const collection = collections.find(c => c.requests[contextMenu.itemId]);
        if (collection && collection.requests[contextMenu.itemId]) {
          currentName = collection.requests[contextMenu.itemId].name;
          collectionId = collection.id;
        }
      }

      if (currentName && collectionId) {
        setRenameDialogData({
          itemType: contextMenu.itemType,
          itemId: contextMenu.itemId,
          collectionId,
          currentName,
        });
        setRenameDialogOpen(true);
        handleContextMenuClose();
      }
    }
  }, [contextMenu, collections, handleContextMenuClose]);

  // Handle delete folder
  const handleDeleteFolder = useCallback(async (collectionId: string, folderId: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      // Remove the folder from the collection structure
      const removeFolderRecursively = (folders: ApiFolder[]): ApiFolder[] => {
        return folders
          .filter(folder => folder.id !== folderId)
          .map(folder => ({
            ...folder,
            folders: removeFolderRecursively(folder.folders),
          }));
      };

      const updatedCollection = {
        ...collection,
        folders: removeFolderRecursively(collection.folders),
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Clear selected item if it was in this folder
      if (selectedItemId === folderId) {
        handleItemSelect('');
      }

      // TODO: Update the collection in the database
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  }, [collections, selectedItemId, setCollections, handleItemSelect]);

  // Handle delete request
  const handleDeleteRequest = useCallback(async (collectionId: string, requestId: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      // Remove the request from the requests object
      const updatedRequests = { ...collection.requests };
      delete updatedRequests[requestId];

      // Remove the request from any folders that contain it
      const removeRequestFromFolders = (folders: ApiFolder[]): ApiFolder[] => {
        return folders.map(folder => ({
          ...folder,
          requests: folder.requests.filter(reqId => reqId !== requestId),
          folders: removeRequestFromFolders(folder.folders),
        }));
      };

      // Remove from orphaned requests if it's there
      const updatedOrphanedRequests = (collection._orphanedRequests || [])
        .filter(reqId => reqId !== requestId);

      const updatedCollection = {
        ...collection,
        requests: updatedRequests,
        folders: removeRequestFromFolders(collection.folders),
        _orphanedRequests: updatedOrphanedRequests,
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // Clear selected item if it was this request
      if (selectedItemId === requestId) {
        handleItemSelect('');
        setCurrentRequest(undefined);
      }

      // TODO: Update the collection in the database
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  }, [collections, selectedItemId, setCollections, setCurrentRequest, handleItemSelect]);

  // Handle delete folder from context
  const handleDeleteFolderFromContext = useCallback(() => {
    if (contextMenu && contextMenu.itemType === 'folder') {
      // Find the collection that contains this folder
      const collection = collections.find(c => {
        const findFolder = (folders: ApiFolder[]): boolean => {
          return folders.some(f =>
            f.id === contextMenu.itemId ||
            (f.folders.length > 0 && findFolder(f.folders))
          );
        };
        return findFolder(c.folders);
      });

      if (collection) {
        handleDeleteFolder(collection.id, contextMenu.itemId);
      }
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, handleDeleteFolder]);

  // Handle delete request from context
  const handleDeleteRequestFromContext = useCallback(() => {
    if (contextMenu && contextMenu.itemType === 'request') {
      // Find the collection that contains this request
      const collection = collections.find(c => c.requests[contextMenu.itemId]);
      if (collection) {
        handleDeleteRequest(collection.id, contextMenu.itemId);
      }
      handleContextMenuClose();
    }
  }, [contextMenu, collections, handleContextMenuClose, handleDeleteRequest]);

  // Handle send request with environment
  const handleSendRequestWithEnvironment = useCallback(async () => {
    const currentEnv = environments.find(env => env.id === currentEnvironment);
    return handleSendRequest(currentEnv);
  }, [handleSendRequest, environments, currentEnvironment]);

  // Load selected request when item is selected
  React.useEffect(() => {
    if (selectedItemId) {
      const collection = collections.find(col => {
        return Object.keys(col.requests).includes(selectedItemId);
      });

      if (collection && collection.requests[selectedItemId]) {
        setCurrentRequest(collection.requests[selectedItemId]);
      }
    }
  }, [selectedItemId, collections, setCurrentRequest]);

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections}
              collectionsLoading={collectionsLoading}
              collectionsError={collectionsError ?? null}
              expandedFolders={expandedFolders}
              selectedItemId={selectedItemId}
              unsavedCollections={unsavedCollections}
              isSaving={isSaving}
              onFolderToggle={handleFolderToggle}
              onItemSelect={handleItemSelect}
              onContextMenu={handleContextMenu}
              onAddCollection={handleAddCollection}
              onSaveCollection={handleSaveCollection}
              onSaveAllCollections={handleSaveAllCollections}
            />
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            {/* Environment Manager */}
            <EnvironmentManager
              environments={environments}
              currentEnvironment={currentEnvironment}
              selectedEnvironmentId={selectedEnvironmentId}
              environmentDialogOpen={isEnvironmentDialogOpen}
              onEnvironmentChange={handleEnvironmentChange}
              onOpenEnvironmentDialog={() => setIsEnvironmentDialogOpen(true)}
              onCloseEnvironmentDialog={() => setIsEnvironmentDialogOpen(false)}
              onOpenExportDialog={() => setExportDialogOpen(true)}
              onEnvironmentUpdate={setEnvironments}
              onSelectedEnvironmentChange={setSelectedEnvironmentId}
            />

            {/* Resizable Request and Response Panels */}
            <Box className={classes.requestResponseContainer}>
              <ResizablePanels
                defaultTopHeight={60}
                minTopHeight={25}
                minBottomHeight={20}
                allowCollapse
                topPanel={
                  <RequestBuilder
                    currentRequest={currentRequest}
                    currentResponse={currentResponse}
                    isLoading={isLoading}
                    tabValue={tabValue}
                    currentEnvironment={environments.find(env => env.id === currentEnvironment)}
                    onRequestChange={setCurrentRequest}
                    onMethodChange={handleMethodChange}
                    onUrlChange={handleUrlChange}
                    onTabChange={handleTabChange}
                    onSendRequest={handleSendRequestWithEnvironment}
                    onSavePreRequestScript={handleSavePreRequestScript}
                    onSaveTests={handleSaveTests}
                    onRunTests={handleRunTests}
                    onGenerateTests={handleGenerateTests}
                    isSavingPreRequestScript={isSavingPreRequestScript}
                    preRequestScriptError={preRequestScriptError}
                    isGeneratingTests={isGeneratingTests}
                    isRunningTests={isRunningTests}
                    testError={testError}
                  />
                }
                bottomPanel={
                  <ResponseViewer
                    response={currentResponse}
                    responseTabValue={responseTabValue}
                    onResponseTabChange={handleResponseTabChange}
                  />
                }
              />
            </Box>
          </Grid>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Import Dialog */}
      <ImportDialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}
      />

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        collections={collections}
        environments={environments}
      />

      {/* Context Menu */}
      <Menu
        keepMounted
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {contextMenu?.itemType === 'collection' && (
          <>
            <MenuItem onClick={handleAddFolderFromContext}>
              Add Folder
            </MenuItem>
            <MenuItem onClick={handleAddRequestFromContext}>
              Add Request
            </MenuItem>
            <MenuItem onClick={handleRenameFromContext}>
              Rename Collection
            </MenuItem>
          </>
        )}

        {contextMenu?.itemType === 'folder' && (
          <>
            <MenuItem onClick={handleAddFolderFromContext}>
              Add Folder
            </MenuItem>
            <MenuItem onClick={handleAddRequestFromContext}>
              Add Request
            </MenuItem>
            <MenuItem onClick={handleRenameFromContext}>
              Rename Folder
            </MenuItem>
            <MenuItem onClick={handleDeleteFolderFromContext}>
              Delete Folder
            </MenuItem>
          </>
        )}

        {contextMenu?.itemType === 'request' && (
          <>
            <MenuItem onClick={handleRenameFromContext}>
              Rename Request
            </MenuItem>
            <MenuItem onClick={handleDeleteRequestFromContext}>
              Delete Request
            </MenuItem>
          </>
        )}
      </Menu>

      {/* Create Folder Dialog */}
      <CreateFolderDialog
        open={createFolderDialogOpen}
        onClose={() => setCreateFolderDialogOpen(false)}
        onCreateFolder={(folderName, parentId, collectionId) => {
          handleAddFolder(collectionId, parentId || undefined, folderName);
        }}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Create Request Dialog */}
      <CreateRequestDialog
        open={createRequestDialogOpen}
        onClose={() => setCreateRequestDialogOpen(false)}
        onCreateRequest={(requestName, method, url, parentId, collectionId) => {
          handleAddRequest(collectionId, parentId || undefined, requestName, method, url);
        }}
        collections={collections}
        selectedCollectionId={selectedCollectionForAction}
        selectedFolderId={selectedFolderForAction}
      />

      {/* Rename Dialog */}
      {renameDialogData && (
        <RenameDialog
          open={renameDialogOpen}
          onClose={() => {
            setRenameDialogOpen(false);
            setRenameDialogData(null);
          }}
          onRename={(newName) => {
            if (renameDialogData) {
              if (renameDialogData.itemType === 'collection') {
                handleRenameCollection(renameDialogData.itemId, newName);
              } else if (renameDialogData.itemType === 'folder') {
                handleRenameFolder(renameDialogData.collectionId, renameDialogData.itemId, newName);
              } else if (renameDialogData.itemType === 'request') {
                handleRenameRequest(renameDialogData.collectionId, renameDialogData.itemId, newName);
              }
            }
          }}
          itemType={renameDialogData.itemType}
          currentName={renameDialogData.currentName}
        />
      )}
    </div>
  );
};